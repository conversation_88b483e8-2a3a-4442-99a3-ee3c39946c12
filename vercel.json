{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubDomains; preload"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/(.*)\\.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.woff", "headers": [{"key": "Cache-Control", "value": "public, max-age=63072000, immutable"}]}, {"source": "/(.*)\\.woff2", "headers": [{"key": "Cache-Control", "value": "public, max-age=63072000, immutable"}]}, {"source": "/(.*)\\.ttf", "headers": [{"key": "Cache-Control", "value": "public, max-age=63072000, immutable"}]}, {"source": "/(.*)\\.eot", "headers": [{"key": "Cache-Control", "value": "public, max-age=63072000, immutable"}]}, {"source": "/(.*)\\.png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.jpeg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.gif", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.webp", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.avif", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}], "regions": ["iad1"], "github": {"silent": true}}