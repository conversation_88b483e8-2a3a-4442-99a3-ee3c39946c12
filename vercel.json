{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.(?:woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.(?:png|jpg|jpeg|gif|webp|avif|ico|svg)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}], "regions": ["iad1"], "github": {"silent": true}}