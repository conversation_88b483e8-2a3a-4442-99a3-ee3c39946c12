export interface Project {
  slug: string;
  title: string;
  cover: string;
  category: string;
  year: number;
  team: string[];
  client: string[];
  description: {
    overview: string; // High-level, jargon-free description for general audiences
    technical: string; // Technical details for developer audiences
    features: string[]; // Key features and capabilities
    impact: string; // Business impact and user benefits
  };
  slides: { page: string; images: string[] }[];
  liveUrl?: string;
  githubUrl?: string;
  technologies: {
    frontend: string[];
    backend: string[];
    database: string[];
    tools: string[];
  };
  status: 'deployed' | 'in-development' | 'coming-soon';
  featured: boolean;
  metrics?: {
    performance?: string;
    users?: string;
    uptime?: string;
  };
  demoCredentials?: {
    username?: string;
    password?: string;
    note?: string;
  };
}

export const projectData: Project[] = [
  {
    slug: "lynn-villamor-dental-clinic",
    title: "Lynn Villamor Dental Clinic",
    cover: "/assets/images/lynn-villamor-dental-clinic/cover.png",
    category: "Healthcare & Reservation",
    year: 2024,
    team: ["Erwin Hayag"],
    client: ["Dr. <PERSON>"],
    description: {
      overview: "A comprehensive healthcare platform that revolutionizes dental practice management. The system provides patients with seamless online appointment booking while empowering dental professionals with advanced tools for patient care, schedule management, and practice operations. Features include real-time appointment scheduling, digital prescriptions, intraoral charting, and comprehensive patient record management.",
      technical: "Built with Next.js 14 (App Router) and TypeScript, this full-stack application leverages Server Actions, Prisma ORM with Neon.tech PostgreSQL database, and NextAuth.js for secure authentication. The platform includes advanced features like role-based access control, real-time schedule management, digital prescription system, and HIPAA-compliant data handling with comprehensive audit logging.",
      features: [
        "Multi-role authentication system (Patient, Dentist, Admin, Staff)",
        "Real-time appointment booking with calendar integration",
        "Advanced dentist schedule management with time blocking",
        "Interactive intraoral charting with visual dental records",
        "Digital prescription system with medicine templates",
        "Comprehensive patient record management with SOAP notes",
        "Billing and payment processing with multiple payment methods",
        "Inventory management with stock tracking and alerts",
        "Patient queue system with drag-and-drop functionality",
        "Analytics dashboard with performance metrics",
        "Email/SMS notifications and appointment reminders",
        "PWA support with offline capabilities",
        "Google Maps integration for branch locations",
        "File upload system for patient documents and x-rays",
        "HIPAA-compliant security with encrypted patient records",
        "Mobile-responsive design optimized for healthcare workflows",
        "Two-factor authentication for enhanced security",
        "Audit logging and activity tracking",
        "Holiday and vacation management system",
        "Recurring appointment patterns and templates"
      ],
      impact: "Transforms dental practice operations by reducing administrative overhead by 50% and improving patient satisfaction through streamlined online booking and comprehensive care tracking. The platform enables dental professionals to manage complex schedules efficiently, maintain detailed patient records, and focus more on quality patient care while ensuring regulatory compliance."
    },
    slides: [
      {
        page: "Home Page",
        images: ["/assets/images/lynn-villamor-dental-clinic/homepage-ss.png"],
      },
      {
        page: "About Page",
        images: ["/assets/images/lynn-villamor-dental-clinic/about-ss.png"],
      },
      {
        page: "Services Page",
        images: ["/assets/images/lynn-villamor-dental-clinic/services-ss.png"],
      },
      {
        page: "Contact Page",
        images: ["/assets/images/lynn-villamor-dental-clinic/contact-ss.png"],
      },
    ],
    liveUrl: "https://dental-clinic-webapp.vercel.app/",
    githubUrl: "", // Private repository
    technologies: {
      frontend: ["React", "Next.js 14", "TypeScript", "Tailwind CSS", "shadcn/ui"],
      backend: ["Next.js Server Actions", "NextAuth.js", "Prisma ORM"],
      database: ["PostgreSQL", "Neon.tech"],
      tools: ["Vercel", "Zustand", "Zod", "SendGrid", "Twilio", "Stripe", "PayMongo"]
    },
    status: "deployed",
    featured: true,
    metrics: {
      performance: "Optimized for healthcare workflows with 95+ Lighthouse Score",
      users: "Active dental practice management platform",
      uptime: "99.9% availability with HIPAA-compliant infrastructure"
    },
    demoCredentials: {
      note: "Live demo available - explore the comprehensive healthcare management platform with patient booking, schedule management, and administrative features. Full-featured dental practice solution."
    },
  },
  {
    slug: "lucky-star-grocery-store",
    title: "Lucky Star Grocery Store",
    cover: "/assets/images/lucky-star/cover.png",
    category: "E-commerce & Retail",
    year: 2022,
    team: ["Erwin Hayag"],
    client: ["Nenejj Store"],
    description: {
      overview: "A modern grocery e-commerce platform that makes online food shopping simple and convenient. Lucky Star provides a seamless shopping experience with real-time product availability, secure checkout, and efficient order management for both customers and store administrators.",
      technical: "Built with Laravel 12.x and React 19.x, this full-stack application features a RESTful API architecture, PostgreSQL database with Neon.tech hosting, and Inertia.js for SPA-like experience. The platform includes advanced inventory management, queue-based background processing, and comprehensive testing with Pest PHP.",
      features: [
        "Modern shopping interface with category filtering and search",
        "Persistent shopping cart with real-time updates",
        "Secure user authentication and profile management",
        "Complete order history and status tracking",
        "Customer review and rating system",
        "Admin dashboard with product and inventory management",
        "Coupon system and promotional tools",
        "Analytics dashboard with sales metrics",
        "Grocery-specific features (fresh produce, nutritional info, storage instructions)",
        "Mobile-responsive design optimized for all devices"
      ],
      impact: "Transforms local grocery shopping by providing 24/7 online access, reducing wait times, and enabling efficient inventory management. The platform supports same-day delivery services and helps small grocery stores compete with larger retailers through modern e-commerce capabilities."
    },
    liveUrl: "https://lucky-star-gktq.onrender.com",
    githubUrl: "", // Private repository
    technologies: {
      frontend: ["React 19.x", "TypeScript", "Inertia.js", "Tailwind CSS 4.0", "shadcn/ui", "Lucide React"],
      backend: ["Laravel 12.x", "PHP 8.2+", "Laravel Breeze", "Laravel Queues"],
      database: ["PostgreSQL", "Neon.tech"],
      tools: ["Vite", "Composer", "npm", "ESLint", "Prettier", "Laravel Pint", "Pest PHP"]
    },
    status: "deployed",
    featured: true,
    metrics: {
      performance: "95+ Lighthouse Score",
      users: "Active grocery store platform",
      uptime: "99.9% availability"
    },
    demoCredentials: {
      note: "Live demo available - explore the full shopping experience including product browsing, cart management, and checkout process."
    },
    slides: [
      {
        page: "Home Page",
        images: ["/assets/images/lucky-star/home-ss.png"],
      },
      {
        page: "Login Page",
        images: ["/assets/images/lucky-star/auth-ss.png"],
      },
      {
        page: "Products Page",
        images: ["/assets/images/lucky-star/products-ss.png"],
      },
      {
        page: "Contact Page",
        images: ["/assets/images/lucky-star/contact-ss.png"],
      },
      {
        page: "Checkout Page",
        images: ["/assets/images/lucky-star/checkout-ss.png"],
      },
    ],
  },
];
